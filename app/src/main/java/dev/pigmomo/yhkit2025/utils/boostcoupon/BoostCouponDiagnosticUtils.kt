package dev.pigmomo.yhkit2025.utils.boostcoupon

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.utils.FileUtils
import dev.pigmomo.yhkit2025.utils.GameCodeManager

/**
 * 助力券状态诊断工具
 *
 * 用于诊断助力券相关的状态问题，帮助快速定位文件不为空但获取到空助力参数的原因
 */
object BoostCouponDiagnosticUtils {
    private const val TAG = "BoostCouponDiagnostic"

    /**
     * 诊断数据类
     */
    data class DiagnosticResult(
        val fileStatus: FileStatus,
        val gameCodeStatus: GameCodeStatus,
        val lockStatus: LockStatus,
        val summary: String,
        val recommendations: List<String>
    )

    data class FileStatus(
        val exists: Boolean,
        val size: Long,
        val lineCount: Int,
        val emptyLines: Int,
        val validLines: Int,
        val invalidLines: Int
    )

    data class GameCodeStatus(
        val totalCodes: Int,
        val availableCodes: Int,
        val lockedCodes: Int,
        val codesByPrizeId: Map<String, Int>,
        val codesByPhoneNumber: Map<String, Int>
    )

    data class LockStatus(
        val totalLocks: Int,
        val expiredLocks: Int,
        val activeLocks: Int,
        val oldestLockAge: Long,
        val newestLockAge: Long
    )

    /**
     * 执行完整的助力券状态诊断
     *
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @return 诊断结果
     */
    fun performFullDiagnosis(
        context: Context,
        excludePhoneNumber: String? = null
    ): DiagnosticResult {
        Log.d(TAG, "Starting full boost coupon diagnosis...")

        val fileStatus = diagnoseFileStatus(context)
        val gameCodeStatus = diagnoseGameCodeStatus(context, excludePhoneNumber)
        val lockStatus = diagnoseLockStatus()

        val summary = generateSummary(fileStatus, gameCodeStatus, lockStatus)
        val recommendations = generateRecommendations(fileStatus, gameCodeStatus, lockStatus)

        val result = DiagnosticResult(
            fileStatus = fileStatus,
            gameCodeStatus = gameCodeStatus,
            lockStatus = lockStatus,
            summary = summary,
            recommendations = recommendations
        )

        logDiagnosticResult(result)
        return result
    }

    /**
     * 诊断文件状态
     */
    private fun diagnoseFileStatus(context: Context): FileStatus {
        val fileName = "gameCodeList.txt"
        val exists = FileUtils.fileExists(context, fileName)
        val size = FileUtils.getFileSize(context, fileName)

        if (!exists) {
            return FileStatus(
                exists = false,
                size = 0,
                lineCount = 0,
                emptyLines = 0,
                validLines = 0,
                invalidLines = 0
            )
        }

        val lines = FileUtils.readLines(context, fileName)
        var emptyLines = 0
        var validLines = 0
        var invalidLines = 0

        for (line in lines) {
            when {
                line.trim().isEmpty() -> emptyLines++
                isValidGameCodeLine(line) -> validLines++
                else -> invalidLines++
            }
        }

        return FileStatus(
            exists = true,
            size = size,
            lineCount = lines.size,
            emptyLines = emptyLines,
            validLines = validLines,
            invalidLines = invalidLines
        )
    }

    /**
     * 诊断游戏码状态
     */
    private fun diagnoseGameCodeStatus(context: Context, excludePhoneNumber: String?): GameCodeStatus {
        val allCodes = GameCodeManager.getAllGameCodes(context)
        val filteredCodes = if (excludePhoneNumber != null) {
            allCodes.filter { it.phoneNumber != excludePhoneNumber }
        } else {
            allCodes
        }

        val availableCodes = filteredCodes.count { gameCode ->
            val key = "${gameCode.prizeId}:${gameCode.gameCode}"
            !GameCodeManager.isGameCodeLocked(key)
        }

        val lockedCodes = filteredCodes.size - availableCodes

        val codesByPrizeId = filteredCodes.groupBy { it.prizeId }
            .mapValues { it.value.size }

        val codesByPhoneNumber = filteredCodes.groupBy { it.phoneNumber }
            .mapValues { it.value.size }

        return GameCodeStatus(
            totalCodes = filteredCodes.size,
            availableCodes = availableCodes,
            lockedCodes = lockedCodes,
            codesByPrizeId = codesByPrizeId,
            codesByPhoneNumber = codesByPhoneNumber
        )
    }

    /**
     * 诊断锁定状态
     */
    private fun diagnoseLockStatus(): LockStatus {
        val lockInfo = GameCodeManager.getLockStatusStatistics()

        return LockStatus(
            totalLocks = lockInfo.totalLocks,
            expiredLocks = lockInfo.expiredLocks,
            activeLocks = lockInfo.activeLocks,
            oldestLockAge = lockInfo.oldestLockAge,
            newestLockAge = lockInfo.newestLockAge
        )
    }

    /**
     * 检查游戏码行是否有效
     */
    private fun isValidGameCodeLine(line: String): Boolean {
        val parts = line.split(",")
        if (parts.size < 4) return false

        return parts.all { it.trim().isNotEmpty() }
    }

    /**
     * 生成诊断摘要
     */
    private fun generateSummary(
        fileStatus: FileStatus,
        gameCodeStatus: GameCodeStatus,
        lockStatus: LockStatus
    ): String {
        return buildString {
            appendLine("=== 助力券状态诊断摘要 ===")
            appendLine("文件状态: ${if (fileStatus.exists) "存在" else "不存在"}")
            if (fileStatus.exists) {
                appendLine("文件大小: ${fileStatus.size} bytes")
                appendLine("总行数: ${fileStatus.lineCount}, 有效行: ${fileStatus.validLines}, 无效行: ${fileStatus.invalidLines}")
            }
            appendLine("游戏码总数: ${gameCodeStatus.totalCodes}")
            appendLine("可用游戏码: ${gameCodeStatus.availableCodes}")
            appendLine("锁定游戏码: ${gameCodeStatus.lockedCodes}")
            appendLine("活跃锁定: ${lockStatus.activeLocks}, 过期锁定: ${lockStatus.expiredLocks}")
        }
    }

    /**
     * 生成建议
     */
    private fun generateRecommendations(
        fileStatus: FileStatus,
        gameCodeStatus: GameCodeStatus,
        lockStatus: LockStatus
    ): List<String> {
        val recommendations = mutableListOf<String>()

        // 文件相关建议
        if (!fileStatus.exists) {
            recommendations.add("游戏码文件不存在，请先获取助力券游戏码")
        } else if (fileStatus.size == 0L) {
            recommendations.add("游戏码文件为空，请检查助力券获取逻辑")
        } else if (fileStatus.validLines == 0 && fileStatus.lineCount > 0) {
            recommendations.add("文件存在但没有有效的游戏码行，请检查文件格式")
        } else if (fileStatus.invalidLines > 0) {
            recommendations.add("发现 ${fileStatus.invalidLines} 行无效数据，建议清理文件")
        }

        // 游戏码相关建议
        if (gameCodeStatus.totalCodes == 0) {
            recommendations.add("没有可用的游戏码，请先获取助力券")
        } else if (gameCodeStatus.availableCodes == 0) {
            recommendations.add("所有游戏码都被锁定，请等待或清理过期锁定")
        } else if (gameCodeStatus.availableCodes < 3) {
            recommendations.add("可用游戏码数量较少 (${gameCodeStatus.availableCodes})，建议获取更多助力券")
        }

        // 锁定相关建议
        if (lockStatus.expiredLocks > 0) {
            recommendations.add("发现 ${lockStatus.expiredLocks} 个过期锁定，建议执行清理操作")
        }

        if (lockStatus.activeLocks > gameCodeStatus.totalCodes * 0.8) {
            recommendations.add("锁定比例过高 (${lockStatus.activeLocks}/${gameCodeStatus.totalCodes})，可能存在锁定泄漏")
        }

        if (recommendations.isEmpty()) {
            recommendations.add("状态正常，无需特殊处理")
        }

        return recommendations
    }

    /**
     * 记录诊断结果到日志
     */
    private fun logDiagnosticResult(result: DiagnosticResult) {
        Log.i(TAG, result.summary)
        Log.i(TAG, "建议:")
        result.recommendations.forEach { recommendation ->
            Log.i(TAG, "- $recommendation")
        }
    }

    /**
     * 快速诊断 - 仅检查关键状态
     */
    fun quickDiagnosis(context: Context, excludePhoneNumber: String? = null): String {
        val fileExists = FileUtils.fileExists(context, "gameCodeList.txt")
        val fileSize = FileUtils.getFileSize(context, "gameCodeList.txt")
        val allCodes = GameCodeManager.getAllGameCodes(context)
        val filteredCodes = if (excludePhoneNumber != null) {
            allCodes.filter { it.phoneNumber != excludePhoneNumber }
        } else {
            allCodes
        }

        return "文件${if (fileExists) "存在" else "不存在"} (${fileSize}B), " +
                "游戏码: ${filteredCodes.size}个, " +
                "可用: ${filteredCodes.count { !GameCodeManager.isGameCodeLocked("${it.prizeId}:${it.gameCode}") }}个"
    }
}